<template>
  <div class="wrap flex-c">
    <div class="itemBox" v-for="(item, i) in list" :key="i">
      <div class="name">{{ item.name }}</div>
      <div class="flex-c">
        <div class="dr">
          <div class="label">当日</div>
          <div class="value">{{ item.dr }}{{ item.unit1 }}</div>
        </div>
        <img src="@/assets/img/home/<USER>/divider_vertical.png" class="divider-v" />
        <div class="dr">
          <div class="label">总数</div>
          <div class="value">{{ item.zs }}{{ item.unit2 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        { name: '安全事件', dr: '10', unit1: '个', zs: '108', unit2: '个' },
        { name: '网络攻击', dr: '108', unit1: '次', zs: '72.6', unit2: '万次' },
        { name: '拦截次数', dr: '108', unit1: '次', zs: '71.76', unit2: '万次' },
      ],
    }
  },
}
</script>

<style lang="less" scoped>
.wrap {
  background: linear-gradient(to right, #026ef100, #026ef13b, #026ef100);
  padding: 16px 0 16px 0;
  box-sizing: border-box;
  margin-top: 20px;
  .itemBox {
    margin-right: 66px;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:last-child {
      margin-right: 0;
    }
    .name {
      margin-bottom: 12px;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 30px;
      line-height: 25px;
      letter-spacing: 1px;
      // text-shadow: 0px 5px 10px rgba(0, 0, 0, 0.4);
      text-align: left;
      background: linear-gradient(180deg, #ffffff 0%, #3ac7ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .dr {
      text-align: center;
      .label {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 18px;
        color: #ffffff;
        line-height: 26px;
        text-align: center;
      }
      .value {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 18px;
        line-height: 26px;
        text-align: center;
        background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
.divider-v {
  width: 1px;
  height: 60px;
  margin: 0 30px;
}
</style>