import axios from 'axios'
import { getCookie } from '@/utils/get-cookie'
axios.defaults.headers['Authorization'] = getCookie('Admin-Token')
  ? 'Bearer ' + getCookie('Admin-Token')
  : 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ0eXl3cHQiLCJsb2dpbl91c2VyX2tleSI6IjM0YjEyMWYzLTE2MjQtNDEzMS04NjE4LWY1NjliZDZhYTE4MyJ9.HsIVooCa4yv6v5PZUhn7IY5h0pkgAvxHb0MU701GVLzBoqv-SAV50qOu-WwZ6Gu532BUAX1TUqu9Ni5RvyAKfw' // 让每个请求携带自定义token 请根据实际情况自行修改

const METHOD = {
  GET: 'get',
  POST: 'post',
}

// 创建 axios 实例
const service = axios.create({
  timeout: 60000,
  withCredentials: true,
})

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 未设置状态码则默认成功状态
    console.log(res)
    const code = res.data.code || 200
    // 获取错误信息
    if (code === 401) {
      // window.location.href = process.env.VUE_APP_ADMIN_URL
    } else {
      return res
    }
  },
  (error) => {
    console.log('err' + error)
    let { message } = error
    if (message == 'Network Error') {
      message = '后端接口连接异常'
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时'
    } else if (message.includes('Request failed with status code')) {
      message = '系统接口' + message.substr(message.length - 3) + '异常'
    }
    return Promise.reject(error)
  }
)

/**
 * axios请求
 * @param url 请求地址
 * @param method {METHOD} http method
 * @param params 请求参数
 * @returns {Promise<AxiosResponse<T>>}
 */
async function request(url, method, params, config) {
  url = process.env.VUE_APP_BASE_API + url
  let promise = Promise.resolve(true)
  switch (method) {
    case METHOD.GET:
      promise = service.get(url, { params, ...config })
      break
    case METHOD.POST:
      promise = service.post(url, params, config)
      break
    default:
      promise = service.get(url, { params, ...config })
      break
  }
  return promise.then((res) => {
    return {
      status: res.status,
      statusText: res.statusText,
      data: res.data,
    }
  })
}

async function request2(url, method, params, config) {
  let promise = Promise.resolve(true)
  switch (method) {
    case METHOD.GET:
      promise = service.get(url, { params, ...config })
      break
    case METHOD.POST:
      promise = service.post(url, params, config)
      break
    default:
      promise = service.get(url, { params, ...config })
      break
  }
  return promise.then((res) => {
    return {
      status: res.status,
      statusText: res.statusText,
      data: res.data,
    }
  })
}

async function requestAfdc(url, method, params, config) {
  url = process.env.VUE_APP_BASE_AFDC_URL + url
  let promise = Promise.resolve(true)
  switch (method) {
    case METHOD.GET:
      promise = service.get(url, { params, ...config })
      break
    case METHOD.POST:
      promise = service.post(url, params, config)
      break
    default:
      promise = service.get(url, { params, ...config })
      break
  }
  return promise.then((res) => {
    return {
      status: res.status,
      statusText: res.statusText,
      data: res.data,
    }
  })
}

export { request, request2, requestAfdc }
