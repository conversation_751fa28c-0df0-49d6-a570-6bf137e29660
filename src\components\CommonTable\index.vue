<template>
  <div class="commonTable" :class="[showTwoLine ? 'twoLineTable' : '', cursor ? 'showCursor' : '']">
    <el-table
      :height="height"
      stripe
      ref="table"
      :show-summary="summaryFlag"
      :cell-style="cellStyle"
      :cell-class-name="cellClass"
      :header-cell-style="headerStyle"
      :row-class-name="tableRowClassName"
      :data="tableData.tbody"
      @row-click="rowClick"
      @cell-mouse-enter="handleMouseenter(true)"
      @header-click="headerItemClick"
      @cell-mouse-leave="handleMouseleave(false)"
      :highlight-current-row="highlightCurrentRow"
    >
      <el-table-column v-if="showIndex" label="序号" type="index" width="72" align="center"></el-table-column>
      <template v-for="(item, index) in tableData.thead">
        <el-table-column
          v-if="item.property === 'firstSubmitTime' || item.property === 'rowNumber'"
          align="left"
          :show-overflow-tooltip="tooltip"
          :key="index"
          :min-width="item.width"
          :label="item.label"
          :property="item.property"
        >
          <template slot-scope="scope">
            <div class="group_box">
              <img
                class="table_on_icon"
                v-if="scope.$index == rowClickIndex"
                src="@/assets/img/keyGroups/table_on_icon.png"
                alt=""
              />
              <span>{{ scope.row.firstSubmitTime || scope.row.rowNumber }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.property === 'controlLevel'"
          align="center"
          :show-overflow-tooltip="tooltip"
          :key="index"
          :min-width="item.width"
          :label="item.label"
          :property="item.property"
        >
          <template slot-scope="scope">
            <span class="yellow_linear yellow_linear0" v-if="scope.row.controlLevel == 4">重大</span>
            <span class="yellow_linear yellow_linear1" v-if="scope.row.controlLevel == 3">高</span>
            <span class="yellow_linear yellow_linear2" v-if="scope.row.controlLevel == 2">中</span>
            <span class="yellow_linear yellow_linear3" v-if="scope.row.controlLevel == 1">低</span>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.type === 'status'"
          align="left"
          :show-overflow-tooltip="tooltip"
          :key="index"
          :min-width="item.width"
          :label="item.label"
          :property="item.property"
        >
          <template slot-scope="scope">
            <span :class="`commonTable-status commonTable-status-${scope.row.status}`">{{ scope.row.statusName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.level"
          align="left"
          :show-overflow-tooltip="tooltip"
          :key="index"
          :min-width="item.width"
          :label="item.label"
          :property="item.property"
        >
          <template slot-scope="scope">
            <span :class="`commonTable-level-${scope.row.level}`">{{ scope.row.levelName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.property === 'pLevel'"
          align="center"
          :show-overflow-tooltip="tooltip"
          :key="index"
          :min-width="item.width"
          :label="item.label"
          :property="item.property"
        >
          <template slot-scope="scope">
            <span class="level-red" v-if="scope.row.pLevel == '红'">{{ scope.row.pLevel }}</span>
            <span class="level-ora" v-else-if="scope.row.pLevel == '橙'">{{ scope.row.pLevel }}</span>
            <span class="level-yel" v-else-if="scope.row.pLevel == '黄'">{{ scope.row.pLevel }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :align="item.align"
          :show-overflow-tooltip="tooltip"
          :key="index"
          :min-width="item.width"
          :label="item.label"
          :property="item.property"
          :formatter="item.formatter"
        ></el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'comTable',
  props: {
    showTwoLine: {
      type: Boolean,
      default: false,
    },
    cursor: {
      type: Boolean,
      default: false,
    },
    showIndex: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    tooltip: {
      type: Boolean,
      default: true,
    },
    summaryFlag: {
      type: Boolean,
      default: false,
    },
    height: {
      type: [String, Number],
      default: '300px',
    },
    tableData: {
      type: Object,
      default: () => {
        return {}
      },
    },
    rowClick: {
      type: Function,
      default: () => {},
    },
    rowClickIndex: {
      type: Number,
      default: -1,
    },
    headerStyle: {
      type: Function,
      default: () => {
        return 'background-color:rgba(11, 100, 195, 0.60);font-size:16px;line-height:40px; color:#fff;border-color:rgba(11, 100, 195, 0.60)'
      },
    },
    highlightCurrentRow: {
      type: Boolean,
      default: false,
    },
    isRoll: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rolltimer: null,
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.mouseleave()
    })
  },
  beforeDestroy() {
    this.mouseenter()
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      row.row_index = rowIndex
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if ((rowIndex + 1) % 2 === 0) {
        return 'background-color:rgba(0, 74, 166, 0.3); font-size: 16px;color:#FFFFFF;border-color:transparent'
      } else {
        return 'background-color:transparent; font-size: 16px;color:#FFFFFF;border-color:transparent'
      }
    },
    cellClass({ row, column, rowIndex, columnIndex }) {
      if (this.rowClickIndex == rowIndex) {
        return 'yellow_linear_table'
      }
    },
    autoRoll(stop) {
      if (stop) {
        clearInterval(this.rolltimer)
        this.rolltimer = null
        return
      }
      const table = this.$refs.table
      const bodyWrapper = table.bodyWrapper
      // 拿到元素后，对元素进行定时增加距离顶部距离，实现滚动效果
      this.rolltimer = setInterval(() => {
        // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
        if (bodyWrapper.clientHeight + bodyWrapper.scrollTop >= bodyWrapper.scrollHeight) {
          bodyWrapper.scrollTop = 0
        } else {
          bodyWrapper.scrollTop++
        }
      }, 5 * 10)
    },
    handleMouseleave() {
      this.isRoll && this.autoRoll(false)
    },
    handleMouseenter() {
      this.isRoll && this.autoRoll(true)
    },
    mouseenter() {
      this.isRoll && this.autoRoll(true)
    },
    mouseleave() {
      this.isRoll && this.autoRoll()
    },
    headerItemClick(e) {
      console.log(e, e.label.data.class, 'headerItemClick')
    },
  },
}
</script>

<style lang="less" scoped>
.commonTable {
  .commonTable-status {
    display: flex;
    align-items: center;
  }
  .commonTable-status:before {
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 10px;
  }
  .commonTable-status-green {
    color: #fff;
    &:before {
      background-color: #22b531;
    }
  }
  .commonTable-status-red1 {
    color: #fff;
    &:before {
      background-color: #ff3677;
    }
  }
  .commonTable-status-red {
    color: #e94c45;
    &:before {
      background-color: #e94c45;
    }
  }
  .commonTable-status-orange {
    color: #e78300;
    &:before {
      background-color: #e78300;
    }
  }
  .commonTable-status-blue {
    color: #007bff;
    &:before {
      background-color: #007bff;
    }
  }
  .commonTable-status-yellow {
    color: #ffff00;
    &:before {
      background-color: #ffff00;
    }
  }
  .commonTable-level-red {
    color: #ff0c3c;
  }
  .commonTable-level-orange {
    color: #ff9c0c;
  }
  .commonTable-level-yellow {
    color: #fff50c;
  }
  .commonTable-level-blue {
    color: #60b2ff;
  }
}
// 去除滚动的滚动条
::v-deep .el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: auto;
}
/deep/.fontBold {
  .cell {
    color: red;
  }
}
/deep/.el-table tr {
  background-color: unset;
  /* background-color: #FFF; */
}
/deep/.el-table .el-table__cell {
  padding: 0;
}
/deep/ .el-table .cell {
  line-height: 48px;
  padding-left: 24px;
}
.twoLineTable /deep/.el-table th.el-table__cell > .cell {
  height: 80px;
  line-height: 24px;
  display: flex;
  align-content: center;
  align-items: center;
}
/deep/.table__cell.is-leaf {
  border-bottom: 1px solid transparent;
}
/deep/ .el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: transparent;
}
/deep/ .el-table,
.el-table__expanded-cell {
  background-color: transparent;
}
/* 表格内背景颜色 */
.el-table th,
.el-table tr,
.el-table td {
  background-color: unset;
}
/deep/ .el-table {
  .current-row {
    background: rgba(0, 182, 243, 0.3);
    > td {
      border-top: 1px solid #00b6f3 !important;
      border-bottom: 1px solid #00b6f3 !important;
    }
  }
}
/deep/.el-table th.el-table__cell > .cell {
  padding-left: 24px;
}
//合计
/deep/.el-table__footer-wrapper {
  .el-table__cell {
    height: 34px;
    background-color: transparent;
    background-image: linear-gradient(0deg, rgba(51, 149, 205, 0.2) 1%, rgba(63, 134, 255, 0.3) 100%);
    font-size: 16px;
    color: #ffffff;
    border-color: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  }
}
/deep/.yellow_linear_table {
  .cell {
    background: linear-gradient(0deg, #fff120 0%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.showCursor {
  /deep/.el-table__row {
    cursor: pointer;
  }
}
.group_box {
  display: flex;
  align-content: center;
  align-items: center;
  .table_on_icon {
    width: 18px;
    height: 21px;
    margin-right: 12px;
  }
}
.yellow_linear0 {
  font-size: 22px;
  font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
  // font-weight: bold;
  line-height: 14px;
  background: linear-gradient(0deg, #FF5F5F 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.yellow_linear1 {
  font-size: 22px;
  font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
  // font-weight: bold;
  line-height: 14px;
  background: linear-gradient(0deg, #FF8234 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.yellow_linear2 {
  font-size: 22px;
  font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
  line-height: 14px;
  background: linear-gradient(0deg, #00A0D4 4%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.yellow_linear3 {
  font-size: 22px;
  font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
  line-height: 14px;
  background: linear-gradient(0deg, #1AA86E 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.level-red {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 22px;
  background: linear-gradient(to top, #ff3434, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.level-ora {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 22px;
  background: linear-gradient(to top, #ff883e, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.level-yel {
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 22px;
  background: linear-gradient(to top, #fbfc02, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
