{"name": "jh-platform-screen", "version": "2.0.0", "private": true, "scripts": {"serve-dev": "vue-cli-service serve --mode development", "serve-prd": "vue-cli-service serve --mode production", "build-dev": "vue-cli-service build --mode development", "build-prd": "vue-cli-service build --mode production --report", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@amap/amap-vue": "^2.0.13", "@cgcs2000/mapbox-gl": "^2.3.0", "@easydarwin/easywasmplayer": "^4.0.13", "@jiaminghi/data-view": "^2.10.0", "@turf/turf": "^6.5.0", "animejs": "^3.2.1", "axios": "^0.19.2", "compression-webpack-plugin": "^6.1.1", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "dingtalk-jsapi": "^3.0.9", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-ui": "^2.15.8", "jquery": "^3.7.0", "js-cookie": "2.2.1", "js-md5": "^0.7.3", "lodash": "^4.17.21", "moment": "^2.29.4", "swiper": "^8.3.1", "vue": "^2.6.14", "vue-countupjs": "^1.0.0", "vue-dynamic-marquee": "^0.1.7", "vue-pubilc-layer": "^2.0.5", "vue-router": "^3.3.4", "vue-seamless-scroll": "^1.1.23", "yarn": "^1.22.19"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-component": "^1.1.1", "babel-polyfill": "^6.26.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.1.2", "less-loader": "^6.1.1", "query-string": "^7.1.1", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"no-debugger": "off", "no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10", "chrome >= 63", "safari >= 12", "ie >= 8"]}