<template>
  <div class="wrap">
    <div class="bg">
      <div
        class="li flex-c"
        v-for="(item, i) in list"
        :key="i"
        :style="{
          background: i < 4 ? 'url(' + leftBg + ')' : 'url(' + rightBg + ')',
        }"
        @click="checkDetail(item)"
      >
        <img :src="item.icon" class="icon" v-if="i < 4" />
        <div class="name">{{ item.name }}:{{ item.num }}{{ item.unit }}</div>
        <img :src="item.icon" class="icon" v-if="i >= 4" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        { name: '单位', num: 1798, unit: '', icon: require('@/assets/img/home/<USER>/dw.png') },
        { name: '应用', num: 1.5, unit: '万', icon: require('@/assets/img/home/<USER>/yy.png') },
        { name: '云资源', num: 3.45, unit: '万', icon: require('@/assets/img/home/<USER>/yzy.png') },
        { name: '人员', num: 817, unit: '', icon: require('@/assets/img/home/<USER>/ry.png') },
        { name: '告警事件', num: 75, unit: '', icon: require('@/assets/img/home/<USER>/gj.png') },
        { name: '发现隐患', num: 1, unit: '', icon: require('@/assets/img/home/<USER>/yh.png') },
        { name: '安全事件', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/aq.png') },
        { name: '处置工单', num: 0, unit: '', icon: require('@/assets/img/home/<USER>/gd.png') },
      ],
      leftBg: require('@/assets/img/home/<USER>/center_item_left.png'),
      rightBg: require('@/assets/img/home/<USER>/center_item_right.png'),
    }
  },
  methods: {
    checkDetail(item) {
      this.$emit('checkDetail', item)
    },
  },
}
</script>

<style lang="less" scoped>
.wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  .bg {
    width: 412px;
    height: 462px;
    background: url('~@/assets/img/home/<USER>/center_bg.png') no-repeat;
    background-size: 100% 100%;
    .li {
      width: 258px;
      height: 52px;
      padding: 8px 30px 8px 30px;
      box-sizing: border-box;
      position: absolute;
      cursor: pointer;
      .icon {
        width: 28px;
        height: 28px;
        margin: 0 16px;
      }
      .name {
        white-space: nowrap;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 22px;
        line-height: 35px;
        text-align: left;
        background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      &:nth-child(1) {
        left: 50px;
        top: 40px;
      }
      &:nth-child(2) {
        left: 0px;
        top: 140px;
      }
      &:nth-child(3) {
        left: 0px;
        top: 240px;
      }
      &:nth-child(4) {
        left: 50px;
        top: 340px;
      }
      &:nth-child(5) {
        right: 50px;
        top: 40px;
        padding-left: 60px;
      }
      &:nth-child(6) {
        right: 0px;
        top: 140px;
        padding-left: 60px;
      }
      &:nth-child(7) {
        right: 0px;
        top: 240px;
        padding-left: 60px;
      }
      &:nth-child(8) {
        right: 50px;
        top: 340px;
        padding-left: 60px;
      }
    }
  }
}
</style>