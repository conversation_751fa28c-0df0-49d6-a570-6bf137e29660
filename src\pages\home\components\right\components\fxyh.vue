<template>
  <div>
    <div class="item">
      <div class="itemList flex-b">
        <div class="liBox flex-b" v-for="(item, i) in list" :key="i">
          <div class="li" style="width: 160px">
            <div class="key">{{ item.name }}</div>
            <div class="value">{{ item.value }}</div>
          </div>
          <img v-if="i !== list.length - 1" src="@/assets/img/home/<USER>/divider_vertical.png" class="divider-v" />
        </div>
      </div>
    </div>

    <div class="secondTitle">
      <div class="title">隐患分类</div>
    </div>
    <div class="chart" id="pieChart"></div>

    <div class="secondTitle">
      <div class="title">单位风险统计</div>
    </div>
    <div class="chart2" id="barChart"></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        { name: '隐患数', value: 3224 },
        { name: '严重隐患数', value: 0 },
        { name: '已处置隐患数', value: 3113 },
      ],
      chartData: [
        { name: 'web漏洞', value: 34 },
        { name: '弱口令', value: 19 },
        { name: '主机漏洞', value: 5 },
        { name: '配置漏洞', value: 4 },
        { name: '异常行为', value: 1 },
      ],
      barChartData: [
        { name: '金华市大数据发展中心', total: 10, value1: 2, value2: 4, vlaue3: 4, value4: 0 },
        { name: '金华市大数据发展中心', total: 8, value1: 2, value2: 4, vlaue3: 0, value4: 0 },
        { name: '金华市', total: 7, value1: 0, value2: 4, vlaue3: 1, value4: 0 },
        { name: '金华市社会治理部', total: 5, value1: 0, value2: 2, vlaue3: 2, value4: 0 },
        { name: '金华市政府办公室（区政府研究室、区数据局）', total: 4, value1: 0, value2: 4, vlaue3: 0, value4: 0 },
      ],
    }
  },
  mounted() {
    this.initPieChart(this.chartData, 'pieChart')
    this.initBarChart(this.barChartData.reverse(), 'barChart')
  },
  methods: {
    initPieChart(data, id) {
      let total = 0
      data.forEach((x) => {
        total += x.value
      })
      const pieChart = echarts.init(document.getElementById(id))

      const option = {
        color: ['#007BFF', '#00EAFF', '#22E197', '#98DC3E', '#FF6A00'],
        title: {
          text: '{a|总数量}\n\n{b|' + total + '}',
          x: '24%',
          y: 'center',
          textStyle: {
            rich: {
              a: {
                fontSize: 16,
                color: '#C1D3E3',
              },
              b: {
                fontSize: 24,
                color: '#fff',
                align: 'center',
              },
            },
            align: 'center',
            verticalAlign: 'middle',
          },
        },
        legend: {
          orient: 'vertial',
          right: '20%',
          top: 'center',
          icon: 'circle',
          itemGap: 12,
          itemWidth: 8,
          textStyle: {
            color: '#C1D3E3',
            fontSize: 14,
            rich: {
              a: {
                width: 80,
              },
            },
          },
          formatter: function (name) {
            var target
            for (var i = 0, l = data.length; i < l; i++) {
              if (data[i].name == name) {
                target = data[i].value
              }
            }
            var arr = ['{a|' + name + '}' + ((target / total) * 100).toFixed(2) + '%']
            return arr.join('\n')
          },
          // formatter: '{name}'
        },
        series: [
          {
            type: 'pie',
            radius: ['78%', '90%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false,
            },
            data: data.map((item) => ({
              name: item.name,
              value: item.value,
            })),
          },
        ],
        graphic: [
          {
            type: 'image',
            id: 'logo',
            left: '16.5%',
            top: '9%',
            z: 1000,
            bounding: 'raw',
            style: {
              image: require('@/assets/img/home/<USER>/pie_bg.png'),
              width: 134,
            },
          },
        ],
      }

      // 使用刚指定的配置项和数据显示图表。
      pieChart.setOption(option)
    },
    initBarChart(data, id) {
      let max = data[0].total
      data.forEach((x) => {
        if (x.total > max) max = x.total
      })
      const barChart = echarts.init(document.getElementById(id))
      const option = {
        color: ['#98DC3E', '#22E197', '#00EAFF', '#007BFF'],
        grid: {
          left: 30,
          right: 30,
          top: 10,
          bottom: 20,
        },
        tooltip: {
          trigger: 'item',
          // axisPointer: { type: 'shadow' },
        },
        legend: {
          data: ['低危', '中危', '高危', '紧急'],
          right: 'center',
          bottom: 0,
          itemWidth: 18,
          itemHeight: 10,
          width: 300,
          textStyle: {
            color: '#C1D3E3',
            fontSize: 16,
          },
        },
        xAxis: {
          type: 'value',
          splitLine: { show: false },
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
        },
        yAxis: {
          type: 'category',
          data: data.map((x) => x.name),
          axisLabel: {
            show: true,
            inside: true,
            splitNumber: 50,
            boundaryGap: [20, 20],
            textStyle: {
              color: '#fff',
              verticalAlign: 'bottom',
              align: 'left',
              fontSize: 16,
              padding: [200, 0, 12, -10],
            },
          },
          axisTick: { show: false },
          axisLine: { show: false },
        },
        series: [
          {
            name: '低危',
            type: 'bar',
            stack: 'total',
            barWidth: 10,
            label: { show: false },
            data: data.map((v) => v.value1),
          },
          {
            name: '中危',
            type: 'bar',
            stack: 'total',
            barWidth: 10,
            label: { show: false },
            data: data.map((v) => v.value2),
          },
          {
            name: '高危',
            type: 'bar',
            stack: 'total',
            barWidth: 10,
            label: { show: false },
            data: data.map((v) => v.vlaue3),
          },
          {
            name: '紧急',
            type: 'bar',
            stack: 'total',
            barWidth: 10,
            label: {
              show: false,
            },
            data: data.map((v) => v.value4),
          },
          {
            name: '总共',
            type: 'bar',
            barGap: '-100%',
            barWidth: 10,
            data: data.map((v) => max),
            label: {
              show: true,
              position: 'right',
              align:'center',
              offset: [-20, -20],
              fontSize: 16,
              color: '#fff',
              formatter: (params) => {
                return data[params.dataIndex].total
              },
            },
            itemStyle: {
              color: 'rgba(44, 62, 80, 0.3)',
            },
            tooltip:{show:false},
            z: 0,
            silent: true,
          },
        ],
      }

      barChart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.item {
  background: linear-gradient(to right, #026ef100, #026ef13b, #026ef100);
  margin: 10px 0 16px 0;
  padding: 12px;
  box-sizing: border-box;
  .itemList {
    padding-top: 12px;
    box-sizing: border-box;
    .liBox {
      // flex: 1;
      .li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        box-sizing: border-box;
        .key {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 23px;
          text-align: center;
          white-space: nowrap;
        }
        .value {
          margin-top: 4px;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 24px;
          color: #3fd9ff;
          line-height: 35px;
          text-align: center;
          background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
.divider-v {
  width: 1px;
  height: 60px;
}

.secondTitle {
  width: 140px;
  height: 24px;
  background: url('@/assets/img/home/<USER>/title_bg.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  margin: 24px 8px 10px 8px;
  .title {
    position: absolute;
    left: 30px;
    bottom: 4px;
    white-space: nowrap;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 24px;
    line-height: 30px;
    text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.4);
    text-align: left;
    background: linear-gradient(180deg, #ffffff 0%, #b9ebff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.chart {
  width: 100%;
  height: 160px;
}
.chart2 {
  width: 100%;
  height: 240px;
}
</style>