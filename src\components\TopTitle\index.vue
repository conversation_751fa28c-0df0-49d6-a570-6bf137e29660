<template>
  <div class="topTitle">
    <div class="topTitle-right">
      <div class="temp_left">
        <div class="name">统计日期</div>
        <el-date-picker
          style="width: 220px"
          v-model="dateValue"
          @change="changeDate"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </div>
      <div class="temp_right">
        <img src="@/assets/img/common/admin_icon.png" alt="" @click="go" />
      </div>
    </div>
    <div class="goIndex">{{ title }}</div>
  </div>
</template>

<script>
import {} from '@/api/common'
export default {
  name: 'topTitle',
  props: {
    title: {
      type: String,
      default: '金华市统一运维平台',
    },
  },
  components: {},
  data() {
    return {
      dateValue: [], //时间选择器
    }
  },
  mounted() {
    this.$nextTick(() => {})
  },
  methods: {
    //时间选择器选择
    changeDate(val) {
      
    },
    go() {
      window.open(process.env.VUE_APP_ADMIN_URL, '_blank')
    },
  },
}
</script>

<style lang="less" scoped>
.topTitle {
  background-image: url('@/assets/img/common/topbt.png');
  width: 100%;
  height: 86px;
  margin-bottom: 7px;
  background-size: cover;
  display: flex;
  justify-content: right;
  position: relative;

  .topTitle-right {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 86px;
    width: 100%;

    .temp_left {
      display: flex;
      align-content: center;
      align-items: center;
      margin-left: 23px;

      .name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
        line-height: 28px;
        margin-right: 10px;
      }

      /deep/.el-range-editor {
        background: rgba(5, 89, 172, 0.3);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #7fb5ef;

        .el-range-input {
          background: transparent;
          font-size: 14px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #ffffff;
          height: 32px;
          line-height: 32px;
        }
        .el-range-separator {
          color: #ffffff;
        }
      }
    }

    .temp_right {
      img {
        width: 133px;
        height: 32px;
      }
    }
  }
}
.goIndex {
  position: absolute;
  width: 50%;
  height: 80px;
  // background-color: #09f;
  left: 27%;
  z-index: 10;
  cursor: pointer;
  text-align: center;
  font-size: 48px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  line-height: 72px;
  // text-shadow: 0px 4px 0px #04122f;
  background: linear-gradient(180deg, #ffffff 32%, #52c3f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
