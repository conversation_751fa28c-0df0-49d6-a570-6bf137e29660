<template>
  <div class="right-container">
    <FirstTitle :title="'事件处置情况'"></FirstTitle>
    <sjczqk></sjczqk>
    <FirstTitle :title="'风险隐患'"></FirstTitle>
    <fxyh></fxyh>
  </div>
</template>

<script>
import FirstTitle from '@/components/FirstTitle/index.vue'
import sjczqk from '@/pages/home/<USER>/right/components/sjczqk.vue'
import fxyh from '@/pages/home/<USER>/right/components/fxyh.vue'

export default {
  components: { FirstTitle, sjczqk, fxyh },

  data() {
    return {}
  },
}
</script>

<style lang="less" scoped>
.right-container {
  width: 500px;
  height: 950px;
  background-color: #0b13204d;
}
</style>