<template>
  <div class="center-container">
    <top></top>
    <center @checkDetail="checkDetail"></center>
    <bottom></bottom>

    <el-dialog :visible.sync="show" :modal="false" :show-close="false" class="dialog">
      <div class="header11 flex-b">
        <div class="flex-c">
          <div class="name">{{ listName }}列表</div>
          <div class="btn flex-c-c">查看更多</div>
        </div>
        <img src="@/assets/img/home/<USER>/dialog_close.png" @click="show = false" class="closebtn" />
      </div>
      <div class="tabList flex-c-c">
        <div
          class="tab flex-c-c"
          :class="tabIndex == i ? 'tab_active' : ''"
          v-for="(x, i) in tabList"
          :key="i"
          @click="changeTab(i)"
        >
          {{ x }}
        </div>
      </div>
      <div class="table">
        <div class="tr flex-c">
          <div class="th" style="flex: 2">单位名称</div>
          <div class="th" style="flex: 2">区域</div>
          <div class="th" style="flex: 1">所属行业</div>
          <div class="th" style="flex: 1">应用系统</div>
          <div class="th" style="flex: 1">资产数</div>
          <div class="th" style="flex: 1">系统备案</div>
        </div>
        <div class="tr flex-c" v-for="(item, i) in datalist" :key="i">
          <div class="td" style="flex: 2">{{ item.dwmc }}</div>
          <div class="td" style="flex: 2">{{ item.qy }}</div>
          <div class="td" style="flex: 1">{{ item.sshy }}</div>
          <div class="td" style="flex: 1">{{ item.yyxt }}</div>
          <div class="td" style="flex: 1">{{ item.zcs }}</div>
          <div class="td" style="flex: 1">{{ item.xtba }}</div>
        </div>
      </div>
      <div class="footer flex-b">
        <div class="total flex-c">
          <div>{{ listName }}总数：</div>
          <div class="num flex-c">
            <div class="numItem flex-c-c" v-for="(x, j) in numList" :key="j">{{ x }}</div>
          </div>
        </div>
        <el-pagination
          class="pagination"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page.sync="params.pageNum"
          :total="total"
          :page-size="10"
          :page-sizes="[10, 20, 30, 40]"
          @current-change="changePage"
          @size-change="handleSizeChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import top from '@/pages/home/<USER>/center/components/top.vue'
import center from '@/pages/home/<USER>/center/components/center.vue'
import bottom from '@/pages/home/<USER>/center/components/bottom.vue'
export default {
  components: { top, center, bottom },
  data() {
    return {
      show: false,
      listName: '',
      tabList: ['全部', '重点'],
      tabIndex: 0,
      datalist: [
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
        { dwmc: '永康市大数据中心', qy: '金华市、永康市', sshy: '政府', yyxt: '126', zcs: '478', xtba: '126' },
      ],
      total: 1798,
      params: {
        pageNum: 1,
        pageSize: 10,
      },
    }
  },
  computed: {
    numList() {
      let arr = []
      let total = this.total
      while (total !== 0) {
        arr.push(total % 10)
        total = parseInt(total / 10)
      }
      return arr.reverse()
    },
  },
  methods: {
    changeTab(i) {
      this.tabIndex = i
    },
    checkDetail(value) {
      this.show = true
      this.listName = value.name
    },
    getList() {},
    changePage(pageNum) {
      this.params.pageNum = pageNum
      this.getList()
    },
    handleSizeChange(pageSize) {
      this.params.pageSize = pageSize
      this.getList()
    },
  },
}
</script>

<style lang="less" scoped>
.center-container {
  width: 840px;
  height: 990px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.dialog {
  .header11 {
    background: url('~@/assets/img/home/<USER>/dialog_titlebg.png');
    background-size: 100% 100%;
    width: 1070px;
    height: 70px;
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 30px;
      color: #ffffff;
      line-height: 26px;
      text-align: left;
      padding: 0 30px;
      box-sizing: border-box;
    }
    .btn {
      padding: 10px 18px;
      box-sizing: border-box;
      background: linear-gradient(360deg, #003ca6 0%, #387af0 100%);
      border-radius: 6px 6px 6px 6px;
      border: 1px solid #3998f0;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 19px;
      color: #ffffff;
      line-height: 19px;
    }
    .closebtn {
      width: 25px;
      height: 25px;
      margin: 30px;
    }
  }
  .tabList {
    margin: 20px 20px;
    .tab {
      width: 140px;
      height: 40px;
      background: #05224d;
      border: 1px solid #1c314d;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 18px;
      color: #acc3de;
      line-height: 16px;
      text-align: left;
      -webkit-text-stroke: 1px rgba(0, 0, 0, 0);
      cursor: pointer;
    }
    .tab_active {
      width: 140px;
      height: 40px;
      background: #074db2;
      border: 1px solid #5a85b0;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #ffffff;
      line-height: 16px;
      text-align: left;
    }
  }
  .table {
    padding: 0 30px;
    box-sizing: border-box;
    height: 360px;
    overflow-y: scroll;
    .tr {
      width: 1010px;
      height: 40px;
      background: #052959;
      &:nth-child(2n + 2) {
        background: #05295940;
      }
    }
    .th,
    .td {
      text-align: center;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 16px;
      color: #cde7ff;
      line-height: 23px;
      text-align: center;
    }
  }
  .footer {
    margin: 30px;
    .total {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 20px;
      color: #c1d3e3;
      line-height: 29px;
      text-align: left;
    }
    .num {
      margin: 0 8px;
      .numItem {
        width: 24px;
        height: 34px;
        background: url('@/assets/img/home/<USER>/num_bg.png');
        background-size: 100% 100%;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 20px;
        color: #00eaff;
        line-height: 34px;
        text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.4039);
        text-align: left;
        margin-right: 8px;
        padding-bottom: 4px;
        box-sizing: border-box;
      }
    }
  }
}

/* 自定义el-dialog对话框添加背景图片 */
::v-deep .el-dialog {
  background: transparent;
  background-image: url('~@/assets/img/home/<USER>/dialog_bg.png') !important;
  background-size: 100% 100%;
  width: 1070px;
  height: 627px;
  /* 设置el-dialog__header、el-dialog__body、el-dialog__footer背景为透明 */
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    background-color: rgba(255, 255, 255, 0);
    padding: 0 !important;
  }
}

//翻页
.pagination {
  ::v-deep .el-pagination__total {
    color: #eaeced;
  }
  ::v-deep .el-input__inner {
    border-color: #78828f;
    background: transparent;
    color: #eaeced;
  }

  ::v-deep .btn-prev,
  ::v-deep .btn-next {
    color: #fff;
    background: #67859d;
    border-radius: 2px;
    margin: 0 6px;
    min-width: 26px !important;
    height: 30px;
    padding-left: 6px;
    padding-right: 6px;
  }
  ::v-deep .el-pager {
    li.active {
      font-weight: 600;
      background: #0166a6 !important;
    }
    li {
      font-weight: 400;
      min-width: 26px;
      height: 30px;
      background: #67859d !important;
      margin: 0 4px;
      border-radius: 2px;
      color: #fff;
    }
  }
  ::v-deep .el-pagination__jump {
    color: white;
  }
}
</style>