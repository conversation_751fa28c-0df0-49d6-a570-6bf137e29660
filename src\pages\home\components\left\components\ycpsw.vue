<template>
  <div>
    <div class="item flex-b">
      <div class="flex-c">
        <img :src="list[0].icon" class="icon" />
        <div class="name">{{ list[0].name }}</div>
      </div>
      <div class="num">{{ list[0].value }}{{ list[0].unit }}</div>
    </div>
    <div style="display: flex; justify-content: space-around; align-items: center">
      <div class="flex-c" v-for="(item, i) in list1" :key="i">
        <div class="chart" :id="'pieChart' + (i + 1)"></div>
        <div class="info">
          <div>总量：{{ item.zl }}</div>
          <div>已使用：{{ item.ysy }}</div>
          <div>剩余：{{ item.sy }}</div>
        </div>
      </div>
    </div>
    <div class="item flex-b">
      <div class="flex-c">
        <img :src="list[1].icon" class="icon" />
        <div class="name">{{ list[1].name }}</div>
      </div>
      <div class="num">{{ list[1].value }}{{ list[1].unit }}</div>
    </div>
    <div style="display: flex; justify-content: space-around; align-items: center">
      <div class="chart-align" v-for="(item, i) in list2" :key="i">
        <div class="chart" :id="'pieChart' + (i + 3)"></div>
        <div class="info2">
          <div>总量：{{ item.zl }}</div>
          <div>已使用：{{ item.ysy }}</div>
          <div>剩余：{{ item.sy }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          name: '虚拟机物理数量',
          value: '234',
          unit: '台',
          icon: require('@/assets/img/home/<USER>/xnj.png'),
        },
        {
          name: '云数据库物理数量',
          value: '32',
          unit: '个',
          icon: require('@/assets/img/home/<USER>/ysjjk.png'),
        },
      ],
      list1: [
        { name: 'CPU', value: 16.44, zl: '53251.00核', ysy: '8752.00核', sy: '44499.00核' },
        { name: '内存', value: 28.22, zl: '15.33TB', ysy: '44.48TB', sy: '113.65TB' },
      ],
      list2: [
        { name: 'CPU', value: 16.44, zl: '53251.00核', ysy: '8752.00核', sy: '44499.00核' },
        { name: '内存', value: 28.22, zl: '15.33TB', ysy: '44.48TB', sy: '113.65TB' },
        { name: '硬盘', value: 0.77, zl: '241826.00TB', ysy: '1870.00TB', sy: '239956.00TB' },
      ],
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.getPieChart(this.list1[0], 'pieChart1', '#00C0FF', '#005571')
      this.getPieChart(this.list1[1], 'pieChart2', '#22E8E8', '#017474')
      this.getPieChart(this.list2[0], 'pieChart3', '#00C0FF', '#005571')
      this.getPieChart(this.list2[1], 'pieChart4', '#22E8E8', '#017474')
      this.getPieChart(this.list2[2], 'pieChart5', '#00C0FF', '#005571')
    },
    getPieChart(data, id, color, colorbg) {
      const that = this
      let myChart = this.$echarts.init(document.getElementById(id))
      let option = {
        title: [
          {
            text: data.name,
            x: '25%',
            y: 'top',
            textStyle: {
              fontWeight: 700,
              color: color,
              fontSize: '20',
              fontFamily: 'Source Han Sans-Bold',
            },
          },
          {
            text: data.value + '%',
            x: '17%',
            y: '50%',
            textStyle: {
              fontWeight: 700,
              color: color,
              fontSize: '16',
              fontFamily: 'Source Han Sans-Bold',
            },
          },
        ],
        // color: that.lineBackground,
        series: [
          {
            type: 'pie',
            radius: ['78%', '90%'],
            center: ['50%', '62%'],
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
            },
            hoverAnimation: false,
            data: [
              {
                value: data.value,
                itemStyle: {
                  color: color,
                  shadowBlur: 8,
                  shadowColor: color,
                  label: {
                    show: false,
                  },
                  labelLine: {
                    show: false,
                  },
                },
              },
              {
                value: 100 - data.value,
                itemStyle: {
                  color: colorbg,
                },
              },
            ],
          },
          {
            type: 'pie',
            radius: ['71%', '72%'],
            center: ['50%', '62%'],
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
            },
            hoverAnimation: false,
            data: [
              {
                value: 0,
                itemStyle: {
                  color: '#6D81C3',
                  label: {
                    show: false,
                  },
                  labelLine: {
                    show: false,
                  },
                },
              },
            ],
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
}
</script>

<style lang="less" scoped>
.item {
  background: linear-gradient(to right, #026ef100, #026ef13b, #026ef100);
  margin: 10px 0 10px 0;
  padding: 12px;
  box-sizing: border-box;
  .img {
    width: 25px;
    height: 28px;
  }
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    line-height: 16px;
    text-align: left;
    margin-left: 14px;
  }
  .num {
    width: 80px;
    height: 35px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 24px;
    line-height: 35px;
    text-align: center;
    background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.chart {
  width: 100px;
  height: 130px;
}
.info {
  margin: 20px 0 0 10px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
  text-align: left;
  & > div {
    margin-bottom: 5px;
  }
}
.chart-align {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .info2 {
    margin-top: 10px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    // text-align: center;
    & > div {
      margin-bottom: 6px;
    }
  }
}
</style>